from panda3d.core import WindowProperties, LVector3, CollisionTraverser, CollisionNode, CollisionHandlerPusher, CollisionBox, BitMask32
from panda3d.core import AmbientLight, DirectionalLight
from direct.showbase.ShowBase import ShowBase
from direct.task import Task
from direct.actor.Actor import Actor
import sys

class Platformer(ShowBase):
    def __init__(self):
        ShowBase.__init__(self)
        self.disableMouse()  # Отключаем стандартное управление камерой

        self.init_player()
        self.init_controls()
        self.init_environment()
        self.init_lighting()
        self.init_collisions()

        self.accept("escape", sys.exit)
        self.taskMgr.add(self.update, "update")

    def init_player(self):
        self.player = self.loader.loadModel("models/box")
        self.player.reparentTo(self.render)
        self.player.setScale(0.5)
        self.player.setPos(0, 0, 5)

        self.velocity = LVector3(0, 0, 0)
        self.gravity = -20
        self.jump_strength = 7
        self.on_ground = False

        self.camera.reparentTo(self.player)
        self.camera.setPos(0, -10, 4)
        self.camera.lookAt(self.player)

    def init_controls(self):
        self.keys = {"w": False, "s": False, "a": False, "d": False, "space": False}
        for key in self.keys:
            self.accept(key, self.set_key, [key, True])
            self.accept(f"{key}-up", self.set_key, [key, False])

        self.win.setClearColor((0.5, 0.7, 1.0, 1))  # голубой фон

    def init_environment(self):
        # Земля
        self.ground = self.loader.loadModel("models/box")
        self.ground.setScale(20, 20, 1)
        self.ground.setPos(0, 0, -1)
        self.ground.reparentTo(self.render)

        # Платформы
        self.platforms = []
        for i in range(5):
            plat = self.loader.loadModel("models/box")
            plat.setScale(3, 3, 0.5)
            plat.setPos(i * 6, 0, 1 + i * 1.5)
            plat.reparentTo(self.render)
            self.platforms.append(plat)

    def init_lighting(self):
        ambient = AmbientLight("ambient")
        ambient.setColor((0.5, 0.5, 0.5, 1))
        self.render.setLight(self.render.attachNewNode(ambient))

        directional = DirectionalLight("directional")
        directional.setColor((1, 1, 1, 1))
        dir_np = self.render.attachNewNode(directional)
        dir_np.setHpr(-30, -60, 0)
        self.render.setLight(dir_np)

    def init_collisions(self):
        self.cTrav = CollisionTraverser()
        self.pusher = CollisionHandlerPusher()

        # Коллизия игрока
        player_cnode = CollisionNode('player')
        player_cnode.addSolid(CollisionBox((0, 0, 0), 0.5, 0.5, 1))
        player_cnode.setFromCollideMask(BitMask32.bit(1))
        player_cnode.setIntoCollideMask(BitMask32.allOff())
        self.player_collider = self.player.attachNewNode(player_cnode)
        self.cTrav.addCollider(self.player_collider, self.pusher)
        self.pusher.addCollider(self.player_collider, self.player)

        # Коллизия с землёй и платформами
        self.add_collider(self.ground)
        for plat in self.platforms:
            self.add_collider(plat)

    def add_collider(self, model):
        cnode = CollisionNode('solid')
        cnode.addSolid(CollisionBox((0, 0, 0), *model.getScale()))
        cnode.setIntoCollideMask(BitMask32.bit(1))
        collider = model.attachNewNode(cnode)

    def set_key(self, key, value):
        self.keys[key] = value

    def update(self, task):
        dt = globalClock.getDt()
        speed = 5

        move = LVector3(0, 0, 0)
        if self.keys["a"]:
            move.x -= speed * dt
        if self.keys["d"]:
            move.x += speed * dt
        if self.keys["w"]:
            move.y += speed * dt
        if self.keys["s"]:
            move.y -= speed * dt

        self.player.setPos(self.player.getPos() + move)

        # Гравитация
        self.velocity.z += self.gravity * dt
        self.player.setZ(self.player.getZ() + self.velocity.z * dt)

        # Прыжок
        if self.keys["space"] and self.on_ground:
            self.velocity.z = self.jump_strength
            self.on_ground = False

        # Примитивная проверка "земли"
        if self.player.getZ() < 0.5:
            self.player.setZ(0.5)
            self.velocity.z = 0
            self.on_ground = True

        return Task.cont

app = Platformer()
app.run()
 