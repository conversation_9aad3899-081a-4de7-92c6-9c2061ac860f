import pygame
import random
import sys

# Initialize Pygame
pygame.init()

# Constants
GRID_WIDTH = 10
GRID_HEIGHT = 20
CELL_SIZE = 30
WINDOW_WIDTH = 800
WINDOW_HEIGHT = 700
GRID_X_OFFSET = 50
GRID_Y_OFFSET = 30

# Colors
BLACK = (0, 0, 0)
WHITE = (255, 255, 255)
CYAN = (0, 255, 255)
BLUE = (0, 0, 255)
ORANGE = (255, 165, 0)
YELLOW = (255, 255, 0)
GREEN = (0, 255, 0)
PURPLE = (128, 0, 128)
RED = (255, 0, 0)
GRAY = (128, 128, 128)

# Tetris pieces
PIECES = {
    'I': [['.....',
           '..#..',
           '..#..',
           '..#..',
           '..#..'], CYAN],
    'O': [['.....',
           '.....',
           '.##..',
           '.##..',
           '.....'], Y<PERSON><PERSON><PERSON>],
    'T': [['.....',
           '.....',
           '.#...',
           '###..',
           '.....'], PURPLE],
    'S': [['.....',
           '.....',
           '.##..',
           '##...',
           '.....'], GREEN],
    'Z': [['.....',
           '.....',
           '##...',
           '.##..',
           '.....'], RED],
    'J': [['.....',
           '.....',
           '#....',
           '###..',
           '.....'], BLUE],
    'L': [['.....',
           '.....',
           '..#..',
           '###..',
           '.....'], ORANGE]
}

class TetrisPiece:
    def __init__(self, piece_type):
        self.type = piece_type
        self.shape = PIECES[piece_type][0]
        self.color = PIECES[piece_type][1]
        self.x = GRID_WIDTH // 2 - 2
        self.y = 0
        self.rotation = 0
    
    def rotate(self):
        # Rotate shape 90 degrees clockwise
        rows = len(self.shape)
        cols = len(self.shape[0])
        rotated = [''] * cols
        for i in range(cols):
            for j in range(rows - 1, -1, -1):
                rotated[i] += self.shape[j][i]
        self.shape = rotated
    
    def get_cells(self):
        cells = []
        for row_idx, row in enumerate(self.shape):
            for col_idx, cell in enumerate(row):
                if cell == '#':
                    cells.append((self.x + col_idx, self.y + row_idx))
        return cells

class TetrisGame:
    def __init__(self):
        self.grid = [[BLACK for _ in range(GRID_WIDTH)] for _ in range(GRID_HEIGHT)]
        self.current_piece = None
        self.next_piece = None
        self.held_piece = None
        self.can_hold = True
        self.score = 0
        self.level = 1
        self.lines_cleared = 0
        self.fall_time = 0
        self.fall_speed = 500
        self.paused = False
        self.game_over = False
        self.spawn_new_piece()
        self.next_piece = TetrisPiece(random.choice(list(PIECES.keys())))
    
    def spawn_new_piece(self):
        if self.next_piece:
            self.current_piece = self.next_piece
        else:
            self.current_piece = TetrisPiece(random.choice(list(PIECES.keys())))
        
        self.next_piece = TetrisPiece(random.choice(list(PIECES.keys())))
        self.can_hold = True
        
        if not self.is_valid_position():
            self.game_over = True
    
    def is_valid_position(self, piece=None, dx=0, dy=0):
        if piece is None:
            piece = self.current_piece
        
        for cell_x, cell_y in piece.get_cells():
            new_x, new_y = cell_x + dx, cell_y + dy
            
            if new_x < 0 or new_x >= GRID_WIDTH or new_y >= GRID_HEIGHT:
                return False
            
            if new_y >= 0 and self.grid[new_y][new_x] != BLACK:
                return False
        
        return True
    
    def place_piece(self):
        for cell_x, cell_y in self.current_piece.get_cells():
            if cell_y >= 0:
                self.grid[cell_y][cell_x] = self.current_piece.color
        
        self.clear_lines()
        self.spawn_new_piece()
    
    def clear_lines(self):
        lines_to_clear = []
        for y in range(GRID_HEIGHT):
            if all(cell != BLACK for cell in self.grid[y]):
                lines_to_clear.append(y)
        
        for y in sorted(lines_to_clear, reverse=True):
            del self.grid[y]
            self.grid.insert(0, [BLACK for _ in range(GRID_WIDTH)])
        
        if lines_to_clear:
            self.lines_cleared += len(lines_to_clear)
            line_scores = {1: 100, 2: 300, 3: 500, 4: 800}
            self.score += line_scores.get(len(lines_to_clear), 0) * self.level
            
            new_level = self.lines_cleared // 10 + 1
            if new_level > self.level:
                self.level = new_level
                self.fall_speed = max(50, 500 - (self.level - 1) * 50)
    
    def move_piece(self, dx, dy):
        if self.is_valid_position(dx=dx, dy=dy):
            self.current_piece.x += dx
            self.current_piece.y += dy
            return True
        return False
    
    def rotate_piece(self):
        original_shape = self.current_piece.shape[:]
        self.current_piece.rotate()
        
        if not self.is_valid_position():
            self.current_piece.shape = original_shape
            return False
        return True
    
    def hard_drop(self):
        drop_distance = 0
        while self.move_piece(0, 1):
            drop_distance += 1
        self.score += drop_distance * 2
        self.place_piece()
    
    def hold_piece(self):
        if not self.can_hold:
            return
        
        if self.held_piece is None:
            self.held_piece = TetrisPiece(self.current_piece.type)
            self.spawn_new_piece()
        else:
            temp_type = self.current_piece.type
            self.current_piece = TetrisPiece(self.held_piece.type)
            self.held_piece = TetrisPiece(temp_type)
        
        self.can_hold = False
    
    def update(self, dt):
        if self.game_over or self.paused:
            return
        
        self.fall_time += dt
        if self.fall_time >= self.fall_speed:
            if not self.move_piece(0, 1):
                self.place_piece()
            self.fall_time = 0
    
    def restart(self):
        self.__init__()

class TetrisRenderer:
    def __init__(self, screen):
        self.screen = screen
        self.font = pygame.font.Font(None, 36)
        self.small_font = pygame.font.Font(None, 24)
    
    def draw_grid(self, game):
        grid_x = 50
        grid_y = 50
        
        # Draw grid background
        grid_rect = pygame.Rect(grid_x, grid_y, GRID_WIDTH * CELL_SIZE, GRID_HEIGHT * CELL_SIZE)
        pygame.draw.rect(self.screen, WHITE, grid_rect)
        pygame.draw.rect(self.screen, BLACK, grid_rect, 2)
        
        # Draw placed pieces
        for y in range(GRID_HEIGHT):
            for x in range(GRID_WIDTH):
                if game.grid[y][x] != BLACK:
                    rect = pygame.Rect(grid_x + x * CELL_SIZE, grid_y + y * CELL_SIZE, CELL_SIZE, CELL_SIZE)
                    pygame.draw.rect(self.screen, game.grid[y][x], rect)
                    pygame.draw.rect(self.screen, BLACK, rect, 1)
        
        # Draw current piece
        if game.current_piece:
            for cell_x, cell_y in game.current_piece.get_cells():
                if 0 <= cell_x < GRID_WIDTH and cell_y >= 0:
                    rect = pygame.Rect(grid_x + cell_x * CELL_SIZE, grid_y + cell_y * CELL_SIZE, CELL_SIZE, CELL_SIZE)
                    pygame.draw.rect(self.screen, game.current_piece.color, rect)
                    pygame.draw.rect(self.screen, BLACK, rect, 1)
    
    def draw_ui(self, game):
        # Score
        score_text = self.font.render(f"Score: {game.score}", True, WHITE)
        self.screen.blit(score_text, (400, 50))
        
        # Level
        level_text = self.font.render(f"Level: {game.level}", True, WHITE)
        self.screen.blit(level_text, (400, 100))
        
        # Lines
        lines_text = self.font.render(f"Lines: {game.lines_cleared}", True, WHITE)
        self.screen.blit(lines_text, (400, 150))
        
        # Next piece
        next_text = self.font.render("Next:", True, WHITE)
        self.screen.blit(next_text, (400, 200))
        
        if game.next_piece:
            self.draw_mini_piece(game.next_piece, 450, 230)
        
        # Held piece
        hold_text = self.font.render("Hold:", True, WHITE)
        self.screen.blit(hold_text, (400, 300))
        
        if game.held_piece:
            self.draw_mini_piece(game.held_piece, 450, 330)
        
        # Controls
        controls = [
            "Controls:",
            "← → : Move",
            "↓ : Soft drop",
            "↑/Z : Rotate",
            "Space: Hard drop",
            "C: Hold",
            "P: Pause",
            "R: Restart"
        ]
        
        for i, text in enumerate(controls):
            color = WHITE if i == 0 else GRAY
            font = self.font if i == 0 else self.small_font
            control_text = font.render(text, True, color)
            self.screen.blit(control_text, (400, 400 + i * 25))
        
        # Pause indicator
        if game.paused:
            pause_text = self.font.render("PAUSED", True, WHITE)
            self.screen.blit(pause_text, (200, 300))
        
        # Game over
        if game.game_over:
            game_over_text = self.font.render("GAME OVER", True, WHITE)
            self.screen.blit(game_over_text, (150, 300))
            restart_text = self.small_font.render("Press R to restart", True, WHITE)
            self.screen.blit(restart_text, (150, 350))
    
    def draw_mini_piece(self, piece, x, y):
        for row_idx, row in enumerate(piece.shape):
            for col_idx, cell in enumerate(row):
                if cell == '#':
                    rect = pygame.Rect(x + col_idx * 15, y + row_idx * 15, 15, 15)
                    pygame.draw.rect(self.screen, piece.color, rect)
                    pygame.draw.rect(self.screen, BLACK, rect, 1)

def main():
    screen = pygame.display.set_mode((WINDOW_WIDTH, WINDOW_HEIGHT))
    pygame.display.set_caption("Tetris")
    clock = pygame.time.Clock()
    
    game = TetrisGame()
    renderer = TetrisRenderer(screen)
    
    running = True
    while running:
        dt = clock.tick(60)
        
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                running = False
            
            elif event.type == pygame.KEYDOWN:
                if event.key == pygame.K_LEFT:
                    game.move_piece(-1, 0)
                elif event.key == pygame.K_RIGHT:
                    game.move_piece(1, 0)
                elif event.key == pygame.K_DOWN:
                    if game.move_piece(0, 1):
                        game.score += 1
                elif event.key == pygame.K_UP or event.key == pygame.K_z:
                    game.rotate_piece()
                elif event.key == pygame.K_SPACE:
                    game.hard_drop()
                elif event.key == pygame.K_c:
                    game.hold_piece()
                elif event.key == pygame.K_p:
                    game.paused = not game.paused
                elif event.key == pygame.K_r:
                    game.restart()
        
        game.update(dt)
        
        screen.fill(BLACK)
        renderer.draw_grid(game)
        renderer.draw_ui(game)
        
        pygame.display.flip()
    
    pygame.quit()
    sys.exit()

if __name__ == "__main__":
    main()
